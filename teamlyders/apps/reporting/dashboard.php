<link rel="stylesheet" type="text/css" href="resource?src=styles/reporting-dashboard.css">
<script src="resource?src=scripts/reporting-dashboard.js"></script>

<!-- Reporting Dashboard -->
<div class="reporting-app-container" id="reportingAppContainer">
    <div class="reporting-app hidden" id="reportingApp">
        <main class="main-content">
            <!-- Header Section -->
            <section class="dashboard-header">
                <div class="header-content">
                    <h1>Reporting Dashboard</h1>
                    <p>Monitor key metrics, access favorite reports, and track system performance</p>
                </div>
            </section>
            
            <!-- Overview Section -->
            <!-- <section class="overview-section">
                <div class="section-header">
                    <h2>Overview</h2>
                </div>
                <p class="section-description">Key performance indicators and system statistics</p>
                <div class="overview-grid" id="overviewGrid">
                    Overview cards will be populated by JavaScript
                </div>
            </section> -->
    
            <!-- Favorites Section -->
            <section class="favorites-section">
                <div class="section-header">
                    <h2>Favorite Reports</h2>
                    <button class="btn-secondary view-all-btn" id="viewAllFavoritesBtn">View All Favorites</button>
                </div>
                <p class="section-description">Quick access to your most used reports</p>
                <div class="favorites-grid" id="favoritesGrid">
                    <!-- Favorite reports will be populated by JavaScript -->
                </div>
                <div class="empty-favorites" id="emptyFavorites">
                    <div class="empty-icon">⭐</div>
                    <h3>No Favorite Reports</h3>
                    <p>Start adding reports to your favorites to see them here.</p>
                    <button class="btn-primary" id="browseReportsBtn">Browse Reports</button>
                </div>
            </section>
    
            <!-- Relevant Report Categories Section -->
            <section class="relevant-categories-section">
                <div class="section-header">
                    <h2>Relevent Report Categories</h2>
                    <button class="btn-secondary view-all-btn" id="viewAllReportsBtn">View All Reports</button>
                </div>
                <p class="section-description">Top categories suggested for your role</p>
                <div class="relevant-categories-grid" id="relevantCategoriesGrid">
                    <!-- Relevant categories will be populated by JavaScript -->
                </div>
            </section>
        </main>
    </div>
</div>